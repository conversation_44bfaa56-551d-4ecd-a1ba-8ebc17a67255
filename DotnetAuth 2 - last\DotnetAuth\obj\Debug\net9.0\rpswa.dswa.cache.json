{"GlobalPropertiesHash": "rZbvMezwT92TGoO4uneXPRQQCBIn76JGsdtUH9J1RqQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vLiouep/l5wpFvV5XoSbirnhw8KvkPQpRxMyTeG9ohQ=", "cF5jAVtfEQxASG3rSK4gHDx3nYZsZFGyrjw9jb/n82k=", "MLfRM3K/nqRCe0c8VDf8HFYdaHNYXwviGUkpiGaJpkI=", "hoxjekKHia1Nll8VCWYTjnYuXtYCszoWttdIrWoKTmY="], "CachedAssets": {"sad0bjlL4/OezXPy+h5RldDaNuuXCRtPPvNEmAK4TU4=": {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03.3537156+00:00"}, "Ca1VqcjZMo3juyVdzZAEIAAS0Le+zVw/mjNiYBvq+DM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03.3537156+00:00"}, "iqozroOtihgBI6LT8bpkm2a/IVlwzQqJ2eo7wpAG3Xg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\dotnetauth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\dotnetauth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03.3537156+00:00"}, "vLiouep/l5wpFvV5XoSbirnhw8KvkPQpRxMyTeG9ohQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03.3537156+00:00"}}, "CachedCopyCandidates": {}}