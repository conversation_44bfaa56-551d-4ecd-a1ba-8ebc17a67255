{"ConnectionStrings": {"ProductionConnection": "${DATABASE_URL}"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Email": {"SmtpHost": "${SMTP_HOST}", "SmtpPort": "${SMTP_PORT}", "Username": "${SMTP_USERNAME}", "Password": "${SMTP_PASSWORD}", "FromEmail": "${SMTP_FROM_EMAIL}", "FromName": "${SMTP_FROM_NAME}"}, "Authentication": {"Google": {"ClientId": "${GOOGLE_CLIENT_ID}", "ClientSecret": "${GOOGLE_CLIENT_SECRET}"}}, "ReCaptcha": {"SiteKey": "${RECAPTCHA_SITE_KEY}", "SecretKey": "${RECAPTCHA_SECRET_KEY}"}, "JwtSettings": {"validIssuer": "${JWT_ISSUER}", "validAudience": "${JWT_AUDIENCE}", "expires": 120, "key": "${JWT_SECRET_KEY}"}}