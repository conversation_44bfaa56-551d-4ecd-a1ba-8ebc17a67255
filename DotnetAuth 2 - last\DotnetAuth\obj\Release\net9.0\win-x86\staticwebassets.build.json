{"Version": 1, "Hash": "vhrHpq14zh5vyRY552rq4B8nIdv4aifATWuwcgz8Pbs=", "Source": "DotnetAuth", "BasePath": "_content/DotnetAuth", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DotnetAuth\\wwwroot", "Source": "DotnetAuth", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03+00:00"}], "Endpoints": [{"Route": "profile-pictures/defaults/default-picture-profile.d90hbt9f7m.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d90hbt9f7m"}, {"Name": "label", "Value": "profile-pictures/defaults/default-picture-profile.png"}, {"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}]}, {"Route": "profile-pictures/defaults/default-picture-profile.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\Hgrb\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}]}]}