{"openapi": "3.0.4", "info": {"title": "DotnetAuth API", "description": "\r\n## Comprehensive Authentication & User Management API\r\n\r\nThis API provides a complete authentication and user management solution with the following features:\r\n\r\n### 🔐 **Authentication Features**\r\n- **User Registration** with email verification\r\n- **Secure Login** with JWT tokens\r\n- **Two-Factor Authentication (2FA)** support\r\n- **Password Reset** functionality\r\n- **External Authentication** (Google OAuth)\r\n- **Token Management** with blacklisting support\r\n\r\n### 👤 **User Management**\r\n- **Profile Management** with picture upload\r\n- **Account Settings** (email, phone number changes)\r\n- **Activity Logging** and login history\r\n- **Role-based Access Control** (<PERSON><PERSON>, Doctor, Patient)\r\n\r\n### 🛡️ **Security Features**\r\n- **JWT Token Authentication** with refresh tokens\r\n- **CAPTCHA Protection** for registration and login\r\n- **Account Lockout** after failed attempts\r\n- **Secure Password Policies** (12+ characters, complexity requirements)\r\n- **Token Blacklisting** for secure logout\r\n\r\n### 📱 **Additional Features**\r\n- **Phone Number Verification**\r\n- **Email Change Verification**\r\n- **Account Activity Tracking**\r\n- **Profile Picture Management**\r\n\r\n### 🔧 **Technical Details**\r\n- Built with **.NET 9.0** and **ASP.NET Core**\r\n- Uses **Entity Framework Core** with SQL Server\r\n- Implements **AutoMapper** for object mapping\r\n- Comprehensive **logging** and **error handling**\r\n", "contact": {"name": "API Support", "url": "https://github.com/yourusername/dotnetauth", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "v1.0"}, "paths": {"/api/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user account", "description": "Creates a new user account and sends email verification OTP", "operationId": "RegisterUser", "requestBody": {"description": "User registration data including personal information, credentials, and CAPTCHA token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}}}, "responses": {"200": {"description": "Registration successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterResponse"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "User already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/login": {"post": {"tags": ["Authentication"], "summary": "Authenticate user and generate access tokens", "description": "Validates user credentials and returns JWT tokens for API access", "operationId": "LoginUser", "requestBody": {"description": "Login credentials including email, password, CAPTCHA token, and remember me option", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Invalid request or unverified account", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Authentication failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "423": {"description": "Account locked", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/verify-otp": {"post": {"tags": ["Authentication"], "summary": "Verify email address using OTP", "description": "Confirms user email address with the OTP sent during registration", "operationId": "VerifyEmailOtp", "requestBody": {"description": "Email and OTP for verification", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}}, "responses": {"200": {"description": "Email verified successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpResponse"}}}}, "400": {"description": "Invalid or expired OTP", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/two-factor-login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}}}}}}, "/api/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/change-email": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/update-phone": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberResponse"}}}}}}}, "/api/verify-phone": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberResponse"}}}}}}}, "/api/user": {"get": {"tags": ["User Management"], "summary": "Get current authenticated user's profile information", "description": "Retrieves complete profile data for the authenticated user", "operationId": "GetCurrentUser", "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentUserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/user/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/logout": {"post": {"tags": ["Authentication"], "summary": "Logout user and invalidate access token", "description": "Invalidates the current user's JWT token by adding it to the blacklist", "operationId": "LogoutUser", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {}}}}, "400": {"description": "No token provided", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/login-history": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginHistoryResponse"}}}}}}}, "/api/account-activity": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountActivityResponse"}}}}}}}, "/api/external-auth/google": {"post": {"tags": ["ExternalAuth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}}}}}}, "/api/external-auth/google/register": {"post": {"tags": ["ExternalAuth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}}}}}}, "/api/profile-pictures/upload": {"post": {"tags": ["ProfilePicture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Picture"], "type": "object", "properties": {"Picture": {"type": "string", "format": "binary"}}}, "encoding": {"Picture": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}}}}}}, "/api/profile-pictures/current": {"get": {"tags": ["ProfilePicture"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}}}}}}, "/api/profile-pictures/history": {"get": {"tags": ["ProfilePicture"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}}}}}}, "/api/2fa/setup": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}}}}}}, "/api/2fa/verify-setup": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}}}}}}, "/api/2fa/disable": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}}}}}}, "/api/2fa/verify": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}}}}}}, "/api/2fa/status": {"get": {"tags": ["TwoFactor"], "responses": {"200": {"description": "OK"}}}}, "/api/2fa/recovery-codes": {"post": {"tags": ["TwoFactor"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AccountActivityEntry": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "activityType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AccountActivityResponse": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/AccountActivityEntry"}, "nullable": true}}, "additionalProperties": false}, "ChangeEmailRequest": {"required": ["newEmail", "password"], "type": "object", "properties": {"newEmail": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CurrentUserResponse": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isEmailConfirmed": {"type": "boolean"}, "createAt": {"type": "string", "format": "date-time"}, "updateAt": {"type": "string", "format": "date-time"}, "accessToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Disable2faRequest": {"type": "object", "properties": {"password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Disable2faResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ErrorResponse": {"type": "object", "properties": {"titel": {"type": "string", "description": "Error title or category", "nullable": true, "example": "Authentication Error"}, "statusCode": {"type": "integer", "description": "HTTP status code", "format": "int32", "example": 400}, "message": {"type": "string", "description": "Human-readable error message", "nullable": true, "example": "Invalid email or password"}, "errorCode": {"type": "string", "description": "Optional error code for programmatic error handling", "nullable": true, "example": "AUTH_001"}, "details": {"type": "string", "description": "Additional error details (typically only in development)", "nullable": true, "example": "Stack trace or detailed error information"}, "timestamp": {"type": "string", "description": "UTC timestamp when the error occurred", "format": "date-time", "example": "2024-01-15T10:30:00Z"}, "requestId": {"type": "string", "description": "Unique request identifier for tracking", "nullable": true, "example": "req_12345"}}, "additionalProperties": false, "description": "Standard error response model"}, "ExternalAuthResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "requiresRegistration": {"type": "boolean"}, "requiresTwoFactor": {"type": "boolean"}}, "additionalProperties": false}, "ExternalUserRegistrationRequest": {"required": ["gender", "idToken", "provider", "role"], "type": "object", "properties": {"provider": {"minLength": 1, "type": "string"}, "idToken": {"minLength": 1, "type": "string"}, "gender": {"enum": [0, 1], "type": "integer", "description": "Gender options for user profiles", "format": "int32"}, "role": {"enum": [0, 1, 2], "type": "integer", "description": "User roles available in the system", "format": "int32"}}, "additionalProperties": false}, "ForgotPasswordRequest": {"required": ["confirmPassword", "email", "newPassword", "oldPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "GoogleAuthRequest": {"required": ["idToken"], "type": "object", "properties": {"idToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginHistoryEntry": {"type": "object", "properties": {"loginTime": {"type": "string", "format": "date-time"}, "ipAddress": {"type": "string", "nullable": true}, "device": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "wasSuccessful": {"type": "boolean"}}, "additionalProperties": false}, "LoginHistoryResponse": {"type": "object", "properties": {"loginHistory": {"type": "array", "items": {"$ref": "#/components/schemas/LoginHistoryEntry"}, "nullable": true}}, "additionalProperties": false}, "ProfilePictureHistoryEntry": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "uploadDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ProfilePictureHistoryResponse": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/components/schemas/ProfilePictureHistoryEntry"}, "nullable": true}}, "additionalProperties": false}, "ProfilePictureResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "uploadDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"required": ["confirmPassword", "newPassword"], "type": "object", "properties": {"newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Setup2faRequest": {"type": "object", "properties": {"twoFactorType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Setup2faResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "secret": {"type": "string", "nullable": true}, "qrCodeUrl": {"type": "string", "nullable": true}, "verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TwoFactorLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "twoFactorCode": {"type": "string", "nullable": true}, "rememberDevice": {"type": "boolean"}}, "additionalProperties": false}, "TwoFactorLoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePhoneNumberRequest": {"required": ["phoneNumber"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "type": "string", "format": "tel"}}, "additionalProperties": false}, "UpdatePhoneNumberResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserLoginRequest": {"required": ["captchaToken", "email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "description": "Registered email address", "format": "email", "example": "<EMAIL>"}, "password": {"minLength": 1, "type": "string", "description": "User's password", "example": "MySecureP@ssw0rd123"}, "rememberMe": {"type": "boolean", "description": "If true, refresh token will be valid for 30 days instead of 1 day", "example": false}, "captchaToken": {"minLength": 1, "type": "string", "description": "CAPTCHA verification token to prevent automated login attempts", "example": "captcha_token_67890"}}, "additionalProperties": false, "description": "User login request model"}, "UserRegisterRequest": {"required": ["captchaToken", "email", "firstName", "gender", "lastName", "password", "role"], "type": "object", "properties": {"firstName": {"maxLength": 50, "minLength": 2, "type": "string", "description": "User's first name (2-50 characters)", "example": "<PERSON>"}, "lastName": {"maxLength": 50, "minLength": 2, "type": "string", "description": "User's last name (2-50 characters)", "example": "<PERSON><PERSON>"}, "email": {"minLength": 1, "type": "string", "description": "Valid email address that will serve as the username", "format": "email", "example": "<EMAIL>"}, "password": {"maxLength": 100, "minLength": 12, "type": "string", "description": "Secure password (min 12 chars, must include uppercase, lowercase, digit, and special character)", "example": "MySecureP@ssw0rd123"}, "gender": {"enum": [0, 1], "type": "integer", "description": "User's gender (Male or Female)", "format": "int32"}, "role": {"enum": [0, 1, 2], "type": "integer", "description": "User role (Doctor or Patient - Admin role cannot be registered via API)", "format": "int32"}, "captchaToken": {"minLength": 1, "type": "string", "description": "CAPTCHA verification token to prevent automated registrations", "example": "captcha_token_12345"}}, "additionalProperties": false, "description": "User registration request model"}, "UserRegisterResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isEmailConfirmed": {"type": "boolean"}, "createAt": {"type": "string", "format": "date-time"}, "updateAt": {"type": "string", "format": "date-time"}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Verify2faSetupRequest": {"type": "object", "properties": {"verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Verify2faSetupResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "recoveryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "VerifyOtpRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifyOtpResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifyPhoneNumberRequest": {"required": ["phoneNumber", "verificationCode"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "type": "string", "format": "tel"}, "verificationCode": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "VerifyPhoneNumberResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "\r\n**JWT Authorization header using the Bear<PERSON> scheme.**\r\n\r\nEnter your JWT token in the text input below.\r\n\r\nExample: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`\r\n\r\n**Note:** Do not include the word 'Bearer' - it will be added automatically.", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}], "tags": [{"name": "<PERSON><PERSON>", "description": "Authentication and User Management"}]}