# Railway Deployment Guide for DotnetAuth API

## 🚀 Quick Deployment Steps

### 1. Prerequisites
- Railway account (sign up at [railway.app](https://railway.app))
- GitHub repository with your code
- Your current configuration values

### 2. Deploy to Railway

1. **Connect to Railway**
   - Go to [railway.app](https://railway.app)
   - Click "Start a New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository

2. **Add PostgreSQL Database**
   - In your Railway project dashboard
   - Click "New Service" → "Database" → "PostgreSQL"
   - Railway will automatically create a PostgreSQL instance

3. **Configure Environment Variables**
   Go to your API service → Variables tab and add:

   ```
   DATABASE_URL=${PGDATABASE_URL}
   
   # JWT Configuration
   JWT_ISSUER=DotnetAuthAPI
   JWT_AUDIENCE=https://your-app-name.up.railway.app
   JWT_SECRET_KEY=ThisIsA32CharactersLongSecretKey!
   
   # Email Configuration (G<PERSON> SMTP)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=jhhg ddwm chdn buaq
   SMTP_FROM_EMAIL=<EMAIL>
   SMTP_FROM_NAME=DotnetAuth
   
   # Google OAuth
   GOOGLE_CLIENT_ID=437477048701-h4helihgj6cqcu86ukvbi5dq8u2csfe0.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=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
   
   # ReCAPTCHA (Test keys - replace with real ones)
   RECAPTCHA_SITE_KEY=6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI
   RECAPTCHA_SECRET_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
   ```

4. **Deploy**
   - Railway will automatically detect the Dockerfile
   - The deployment will start automatically
   - Wait for the build to complete

### 3. Post-Deployment

1. **Get Your API URL**
   - Your API will be available at: `https://your-app-name.up.railway.app`
   - Update the JWT_AUDIENCE variable with this URL

2. **Test the API**
   - Health check: `https://your-app-name.up.railway.app/health`
   - Swagger UI: `https://your-app-name.up.railway.app/swagger`

3. **Update Google OAuth Settings**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Update authorized redirect URIs to include your Railway domain

## 🔧 Configuration Details

### Database Connection
- Railway automatically provides `PGDATABASE_URL` environment variable
- The app automatically detects PostgreSQL and uses Npgsql
- Migrations run automatically on startup in production

### Security Notes
- All sensitive data is now in environment variables
- JWT secret should be changed to a secure 32+ character string
- Replace test CAPTCHA keys with real ones from Google reCAPTCHA

### File Storage
- Profile pictures are stored in `/app/wwwroot/profile-pictures/`
- Railway provides persistent storage for this directory

## 🐛 Troubleshooting

### Common Issues:

1. **Database Connection Errors**
   - Ensure PostgreSQL service is running
   - Check DATABASE_URL environment variable

2. **Migration Errors**
   - Check logs in Railway dashboard
   - Migrations run automatically on startup

3. **Environment Variable Issues**
   - Verify all required variables are set
   - Check for typos in variable names

### Logs
- View logs in Railway dashboard → Service → Logs tab
- Look for startup errors or database connection issues

## 📊 Monitoring

- **Health Check**: `/health` endpoint
- **Logs**: Available in Railway dashboard
- **Metrics**: Railway provides basic metrics

## 💰 Costs

- **Free Tier**: $5/month credit (sufficient for development)
- **PostgreSQL**: Included in free tier
- **Scaling**: Pay-as-you-go beyond free tier

Your API is now live and ready to use! 🎉
